<!--档期详情-->
<template>
  <div class="execution">
    <el-form label-width="100px"  ref="searchFormRef" class="search_form" :model="searchForm">
      <el-row type="flex" justify="start">
        <el-form-item label="拍摄场地" prop="location">
          <el-cascader
            :collapse-tags="true"
            v-model="searchForm.location"
            :options="treeData"
            :props="{ expandTrigger: 'hover',label:'name',children:'roomList',value:'id',multiple:true}"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" @blur="handlePhoneBlur" clearable @clear="handlePhoneClear"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" @click="search">确认搜索</el-button>
          <el-button size="small" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-row>
      <el-row type="flex" justify="start">
        <el-form-item label="时间条件" prop="dateType">
          <el-radio-group v-model="searchForm.dateType">
            <el-radio :label="'0'">特定日期</el-radio>
            <el-radio :label="'1'">日期范围</el-radio>
            <el-radio :label="'2'">不限</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="searchForm.dateType =='0'" label="特定日期" >
          <el-date-picker
            v-model="searchForm.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="searchForm.dateType =='1'" label="日期范围" prop="startTime">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            value-format="yyyy-MM-dd"
            placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
      </el-row>
    </el-form>
    <div style="margin: 10px">
      <avue-crud ref="crud"
                 :page="page"
                 :data="tableData"
                 :permission="permissionList"
                 :table-loading="tableLoading"
                 :option="tableOption"
                 @refresh-change="refreshChange"
                 @row-update="handleUpdate"
                 @row-save="handleSave"
                 @row-del="handleDel"
                 @sort-change="sortChange"
                 @search-change="searchChange"
                 @selection-change="handleSelectionChange">

        <template slot-scope="scope" slot="ableFlag">
            <el-tag v-if="scope.row.ableFlag == '0'" type="success">否</el-tag>
            <el-tag v-if="scope.row.ableFlag == '1'" type="danger">是</el-tag>
        </template>
        <template slot-scope="scope" slot="menuLeft">
          <el-button type="success"
                     icon="el-icon-check"
                     size="small"
                     @click.stop="handleCompleteOrders">完成订单
          </el-button>
          <el-button type="primary"
                     icon="el-icon-plus"
                     size="small"
                     @click.stop="openAppointBox('add')">添加档期
          </el-button>
          <el-button type="danger"  icon="el-icon-delete" size="small" @click="openAppointBox('del')">删除档期
          </el-button>
        </template>
        <template slot-scope="scope"
                  slot="menu">
          <el-button type="text" size="small" @click="openAppointBox('put',scope.row)">更改</el-button>
          <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
        </template>
        <template slot-scope="scope" slot="headimgUrl">
          <div v-if="scope.row.headimgUrl">
            <el-avatar size="small" :src="scope.row.headimgUrl"> </el-avatar>
          </div>
        </template>

        <!-- 自定义订单状态列显示 -->
        <template slot="orderStatusDesc" slot-scope="scope">
          <el-tag
            :type="getOrderStatusTagType(scope.row.orderInfo ? scope.row.orderInfo.statusDesc : null)"
            size="small">
            {{ (scope.row.orderInfo && scope.row.orderInfo.statusDesc) || '-' }}
          </el-tag>
        </template>

        <!-- 自定义退款状态列显示 -->
        <template slot="refundStatus" slot-scope="scope">
          <el-tag
            v-if="getLatestRefundStatus(scope.row)"
            :type="getRefundStatusTagType(getLatestRefundStatus(scope.row))"
            size="small">
            {{ getLatestRefundStatus(scope.row) }}
          </el-tag>
          <span v-else>-</span>
        </template>

        <!-- 自定义购买详情列显示 -->
        <template slot="purchaseDetail" slot-scope="scope">
          <span v-if="getPurchaseDetail(scope.row)">
            {{ getPurchaseDetail(scope.row) }}
          </span>
          <span v-else>-</span>
        </template>
      </avue-crud>
    </div>

    <!-- 档期框 appointBoxVisible-->
    <el-dialog
      :append-to-body="true"
      :title="auctionInfoForm.title"
      :visible.sync="appointBoxVisible"
      width="60%"
      @close="resetAuctionInfoForm"
      center>
      <el-form :model="auctionInfoForm" :rules="auctionInfoFormRules" ref="auctionInfoFormRef" size="small" label-width="100px">
        <el-form-item v-if="auctionInfoForm.type=='add' || auctionInfoForm.type=='put'"  label="拍摄用户" prop="userId">

          <div v-if="auctionInfoForm.userId" class="block" style="display: flex;justify-content: start">
            <el-avatar :size="'large'" :src="auctionInfoForm.headimgUrl"></el-avatar>
            {{ " "+ auctionInfoForm.nickName}}
          </div>
          <el-button v-if="auctionInfoForm.type=='add'  && !auctionInfoForm.userId" type="primary" size="mini" icon="el-icon-plus" @click="openUserBox">指定用户</el-button>
          <el-button v-if="auctionInfoForm.type=='add' && auctionInfoForm.userId" type="primary" size="mini" icon="el-icon-edit" @click="openUserBox">更改用户</el-button>

        </el-form-item>
        <el-form-item label="拍摄场地" prop="location">
          <el-cascader
            v-model="auctionInfoForm.location"
            :options="treeData"
            :props="{ expandTrigger: 'hover',label:'name',children:'roomList',value:'id'}"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="具体日期" prop="date">
          <el-date-picker
            v-model="auctionInfoForm.date"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="具体时间" prop="time">
          <el-time-picker
            is-range
            v-model="auctionInfoForm.time"
            value-format="HH:mm:ss"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围">
          </el-time-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">确认</el-button>
          <el-button @click="resetAuctionInfoForm">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!-- 用户框 userBoxVisible-->
    <el-dialog
      :append-to-body="true"
      title="用户选择"
      :visible.sync="userBoxVisible"
      width="80%"
      center>
      <user-select @ensureUser="ensureUser"></user-select>
    </el-dialog>

    <!-- 完成订单确认对话框 -->
    <el-dialog
      :append-to-body="true"
      title="完成订单确认"
      :visible.sync="completeOrderDialogVisible"
      width="50%"
      center>
      <div v-if="selectedOrderUsers.length > 0">
        <p style="margin-bottom: 20px; font-weight: bold;">确认将这些用户的订单改为已完成吗？</p>
        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
          <div v-for="user in selectedOrderUsers" :key="user.id" style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid #e4e7ed; border-radius: 4px; background-color: #f9f9f9; min-width: 200px;">
            <el-avatar v-if="user.headimgUrl" size="small" :src="user.headimgUrl" style="margin-right: 8px;"></el-avatar>
            <div style="flex: 1;">
              <div style="font-weight: bold; font-size: 14px;">{{ user.nickName || '未知用户' }}</div>
              <div style="color: #909399; font-size: 12px;">{{ user.phone || '无手机号' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <p style="text-align: center; color: #909399;">请先选择要完成订单的用户</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="completeOrderDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCompleteOrders" :disabled="selectedOrderUsers.length === 0">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {getPage, addObj, putObj, delObj} from '@/api/wxmp/auctioncalendar'
import {getShopAndRoomTree} from '@/api/wxmp/shopinfo'
import {mapGetters} from 'vuex'
import {tableOption} from '@/const/crud/viewgen/auctioncalendar'
import userSelect from '@/views/wxmp/wxuser/userSelect';
export default {
  name: 'calendardetail',
  components: {
    userSelect
  },
  props: {
    day: {
      type: String,
    },
    selectedLocation: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    day(val, oldVal) {
      if (val != oldVal) {
        this.dayTemp = val;
        this.initParam();
      }
    },
    selectedLocation: {
      handler(val) {
        // 只有当 selectedLocation 有值且长度大于0时才处理
        if (val && val.length > 0) {
          this.searchForm.location = val;
          
          // 处理场地ID列表，用于API请求
          let roomIdList = [];
          val.forEach(path => {
            if (path.length === 2) {
              roomIdList.push(path[1]);
            }
          });
          
          if (roomIdList.length > 0) {
            this.paramsSearch.roomIdList = roomIdList;
            
            // 如果已经初始化完成，重新获取数据
            if (this.treeData.length > 0) {
              this.getPage(this.page);
            }
          }
        } else {
          // 当 selectedLocation 为空时，清除相关筛选条件
          if (this.searchForm.location) {
            delete this.searchForm.location;
          }
          if (this.paramsSearch.roomIdList) {
            delete this.paramsSearch.roomIdList;
          }
        }
      },
      deep: true
    }
  },
  data() {
    return {
      searchForm: {dateType:'0', phone: ''},//搜索表单
      paramsSearch: {},//表单转为paramsSearch
      detailList: '',//档期细节
      dayTemp: '',//临时时间
      userBoxVisible: false,//  房间信息
      appointBoxVisible: false,
      // 完成订单相关数据
      completeOrderDialogVisible: false, // 完成订单对话框显示状态
      selectedRows: [], // 选中的行数据
      selectedOrderUsers: [], // 选中的有订单的用户数据
      auctionInfoForm: {
        title: '',
        userId: '',
        headimgUrl: "",
        nickName: '',
        type: '',
        date: '',
        time: ["",""], 
        location: '',
        ableFlag: '',
      },//具体档期
      auctionInfoFormRules: {
        userId: [{required: true, message: '请选择一名用户', trigger: 'blur'},],
        location: [{required: true, message: '请选择拍摄地点', trigger: 'blur'},],
        date: [{required: true, message: '请选择拍摄日期', trigger: 'blur'},],
        time: [{required: true, message: '请选择拍摄时间', trigger: 'blur'},],
      },
      tableData: [],
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: ['start_time'],//升序字段
        descs: []//降序字段
      },
      tableLoading: false,
      tableOption: tableOption,
      treeData: [], //店铺和场地List
      defaultProps: {
        children: 'roomList',
        label: 'name'
      },
    }
  },
  created() {
    this.initParam();

  },
  mounted: function () {
  },
  beforeDestroy() {
    // 组件销毁前清空phone参数
    if (this.paramsSearch && this.paramsSearch.phone) {
      delete this.paramsSearch.phone;
    }
    if (this.searchForm) {
      this.searchForm.phone = '';
    }
  },
  computed: {
    ...mapGetters(['permissions']),
    permissionList() {
      return {
        addBtn: this.permissions['weixin:auctioncalendar:add'] ? true : false,
        delBtn: this.permissions['weixin:auctioncalendar:del'] ? true : false,
        editBtn: this.permissions['weixin:auctioncalendar:edit'] ? true : false,
        viewBtn: this.permissions['weixin:auctioncalendar:get'] ? true : false
      };
    }
  },
  methods: {
    // 处理表格多选变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 处理完成订单按钮点击
    handleCompleteOrders() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要完成订单的用户');
        return;
      }

      console.log( this.selectedRows);
      
      // 筛选出有订单信息的用户
      this.selectedOrderUsers = this.selectedRows.filter(row => {
        return row.orderInfo && row.orderInfo.id;
      });

      if (this.selectedOrderUsers.length === 0) {
        this.$message.warning('选中的用户中没有有效的订单信息');
        return;
      }

      this.completeOrderDialogVisible = true;
    },

    // 确认完成订单
    confirmCompleteOrders() {
      // 这里预留调用接口的地方
      const orderIds = this.selectedOrderUsers.map(user => user.orderInfo.id);

      // TODO: 调用完成订单接口
      this.callCompleteOrderApi(orderIds);
    },

    // 调用完成订单接口（预留）
    callCompleteOrderApi(orderIds) {
      // 预留接口调用位置
      console.log('调用完成订单接口，参数：', {
        orderIds: orderIds,
        orderStatus: 12
      });

      // 模拟接口调用成功
      this.$message.success(`成功完成 ${orderIds.length} 个订单`);
      this.completeOrderDialogVisible = false;
      this.selectedRows = [];
      this.selectedOrderUsers = [];

      // 刷新表格数据
      this.getPage(this.page);

      // TODO: 实际接口调用代码
      /*
      const params = orderIds.map(orderId => ({
        orderId: orderId,
        orderStatus: 12
      }));

      // 调用后端接口
      updateOrderStatus(params).then(res => {
        this.$message.success(`成功完成 ${orderIds.length} 个订单`);
        this.completeOrderDialogVisible = false;
        this.selectedRows = [];
        this.selectedOrderUsers = [];
        this.getPage(this.page);
      }).catch(err => {
        this.$message.error('完成订单失败：' + err.message);
      });
      */
    },

    searchChange(params,done) {
      params = this.filterForm(params)
      this.paramsSearch = params
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    getPage(page, params) {
      //表单完善
      if(this.searchForm.dateType == '1'){
        this.paramsSearch.startTime = this.searchForm.dateRange[0] + " 00:00:00";
        this.paramsSearch.endTime = this.searchForm.dateRange[1] + " 00:00:00";
      } else if(this.searchForm.dateType == '0' && this.searchForm.startTime){
        this.paramsSearch.startTime = this.searchForm.startTime + " 00:00:00";
      } else if(this.searchForm.dateType == '2'){
        // 不限时间，删除时间相关参数
        delete this.paramsSearch.startTime;
        delete this.paramsSearch.endTime;
      }
      
      if(this.searchForm.location) {
        let roomIdList = [];
        this.searchForm.location.forEach(o => {
          roomIdList.push(o[1]);
        })
        this.paramsSearch.roomIdList = roomIdList;
      }
      
      this.tableLoading = true
      let obj =Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page.descs,
        ascs: this.page.ascs,
      }, params, this.paramsSearch)

      console.log("请求参数",obj)
      getPage(obj).then(res => {
        // console.log("请求结果", res);
        this.tableData = res.data.data.records;
        this.page.total = res.data.data.total
        this.page.currentPage = page.currentPage
        this.page.pageSize = page.pageSize
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    /**
     * @title 数据删除
     * @param row 为当前的数据
     * @param index 为当前删除数据的行数
     *
     **/
    handleDel: function (row, index) {
      let _this = this
      this.$confirm('是否确认删除此数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(()=> {
        delObj(row.id).then(res=>{
          this.getPage(this.page)
          this.$emit("dateChange")
          return true
        })
      }).catch(function (err) {
      })
    },
    /**
     * @title 数据更新
     * @param row 为当前的数据
     * @param index 为当前更新数据的行数
     * @param done 为表单关闭函数
     *
     **/
    handleUpdate: function (row, index, done, loading) {
      putObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '修改成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    /**
     * @title 数据添加
     * @param row 为当前的数据
     * @param done 为表单关闭函数
     *
     **/
    handleSave: function (row, done, loading) {
      addObj(row).then(res => {
        this.$message({
          showClose: true,
          message: '添加成功',
          type: 'success'
        })
        done()
        this.getPage(this.page)
      }).catch(() => {
        loading()
      })
    },
    //得到某天所有的档期
    initParam() {
      //表单数据
      this.paramsSearch.startTime = this.day +" 00:00:00";
      this.searchForm.startTime = this.day;
      this.searchForm.phone = '';
      
      // 回填选择的场地，只有当 selectedLocation 有值时才处理
      if (this.selectedLocation && this.selectedLocation.length > 0) {
        this.searchForm.location = this.selectedLocation;
        
        // 处理场地ID列表，用于API请求
        let roomIdList = [];
        this.selectedLocation.forEach(path => {
          if (path.length === 2) {
            roomIdList.push(path[1]);
          }
        });
        
        if (roomIdList.length > 0) {
          this.paramsSearch.roomIdList = roomIdList;
        }
      } else {
        // 当 selectedLocation 为空时，清除相关筛选条件
        if (this.searchForm.location) {
          delete this.searchForm.location;
        }
        if (this.paramsSearch.roomIdList) {
          delete this.paramsSearch.roomIdList;
        }
      }
      
      this.getShopAndRoomTree();
      this.getPage(this.page)
    },
    //打开添加档期
    openAppointBox(type,obj) {
      console.log(type)
      this.$set(this.auctionInfoForm, 'type', type)
      this.$set(this.auctionInfoForm, 'date', this.day)
      if(type =='add'){
        this.$set(this.auctionInfoForm, 'title', "新增档期")
        this.$set(this.auctionInfoForm, 'id', "")
        this.$set(this.auctionInfoForm, 'userId', "")
        this.$set(this.auctionInfoForm, 'nickName', "")
        this.$set(this.auctionInfoForm, 'headimgUrl', "")
        this.$set(this.auctionInfoForm, 'time', ["00:00:00","23:00:00"])
      }else if(type =='del'){
        this.$set(this.auctionInfoForm, 'title', "删除档期")
        this.$set(this.auctionInfoForm, 'time', ["00:00:00","23:00:00"])
      }else if(type =='put'){
        this.$set(this.auctionInfoForm, 'title', "修改档期")
        this.$set(this.auctionInfoForm, 'id', obj.id)
        this.$set(this.auctionInfoForm, 'userId', obj.userId)
        this.$set(this.auctionInfoForm, 'nickName', obj.nickName)
        this.$set(this.auctionInfoForm, 'headimgUrl', obj.headimgUrl)
        this.$set(this.auctionInfoForm, 'location', [obj.shopId,obj.roomId])
        this.$set(this.auctionInfoForm, 'date', obj.startTime.split(" ")[0])
        this.$set(this.auctionInfoForm, 'time', [obj.startTime.split(" ")[1] , obj.endTime.split(" ")[1]])
      }
      this.appointBoxVisible = true;

    },
    onSubmit() {
      this.$refs["auctionInfoFormRef"].validate((valid) => {
        if (valid) {
          console.log("参数", this.auctionInfoForm)
          let ableFlag = 0;
          if(this.auctionInfoForm.type == 'add'){
            ableFlag = 0;
          }else if(this.auctionInfoForm.type == 'del'){
            ableFlag = 1;
          }
          let from = {
            id: this.auctionInfoForm.id,
            userId: this.auctionInfoForm.userId,
            shopId: this.auctionInfoForm.location[0],
            roomId: this.auctionInfoForm.location[1],
            startTime: this.auctionInfoForm.date + " " + this.auctionInfoForm.time[0],
            endTime: this.auctionInfoForm.date + " " + this.auctionInfoForm.time[1],
            ableFlag: ableFlag
          }
          console.log("提交参数auctioncalendar", from)
          //del 是逻辑占用  实际为新增
          if(this.auctionInfoForm.type=="add" || this.auctionInfoForm.type=="del" ) {
            addObj(from).then(res => {
              this.$message({
                showClose: true,
                message: '添加成功',
                type: 'success'
              })
              this.getPage(this.page);
              this.$emit("dateChange")
              this.$refs["auctionInfoFormRef"].resetFields();
              this.appointBoxVisible = false;
            }).catch(() => {
            })
          }else if(this.auctionInfoForm.type=="put" ){
            putObj(from).then(res => {
              this.$message({
                showClose: true,
                message: '修改成功',
                type: 'success'
              })
              this.getPage(this.page);
              this.$emit("dateChange")
              this.$refs["auctionInfoFormRef"].resetFields();
              this.appointBoxVisible = false;
            }).catch(() => {
            })
          }

        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetAuctionInfoForm() {
      this.appointBoxVisible = false;
      this.auctionInfoForm={};
      this.$refs["auctionInfoFormRef"].resetFields();
    },
    //得到店铺和场地
    getShopAndRoomTree() {
      getShopAndRoomTree().then(res => {
        console.log("treeData", res);
        this.treeData = res.data.data;
        let shopData =[]
        let roomData =[]
        this.treeData.forEach(shop=>{
          shopData.push({value:shop.id,label:shop.name})
          shop.roomList.forEach(room=>{
            roomData.push({value:room.id,label:room.name})
          })
        })
        this.tableOption.column[4].dicData = shopData;
        this.tableOption.column[5].dicData = roomData;
      }).catch(() => {
      })
    },
    search() {
      // 确保在搜索前处理场地参数
      if(this.searchForm.location) {
        let roomIdList = [];
        this.searchForm.location.forEach(path => {
          if (path.length === 2) {
            roomIdList.push(path[1]);
          }
        });

        if (roomIdList.length > 0) {
          this.paramsSearch.roomIdList = roomIdList;
        }
      }

      this.getPage(this.page);
    },
    resetSearch() {
      this.paramsSearch = {};
      this.searchForm = {
        dateType: '0',
        startTime: this.day,
        phone: ''
      };

      // 保留从父组件传递的场地选择
      if (this.selectedLocation && this.selectedLocation.length > 0) {
        this.searchForm.location = this.selectedLocation;

        let roomIdList = [];
        this.selectedLocation.forEach(path => {
          if (path.length === 2) {
            roomIdList.push(path[1]);
          }
        });

        if (roomIdList.length > 0) {
          this.paramsSearch.roomIdList = roomIdList;
        }
      }

      // 确保phone参数被删除
      delete this.paramsSearch.phone;

      this.getPage(this.page)
    },
    /**
     * 刷新回调
     */
    refreshChange(page) {
      this.getPage(this.page)
    },
    openUserBox(){
      this.userBoxVisible = true;
    },
    //用户框点击确认
    ensureUser(obj){
      console.log("用户确认",obj)
      this.auctionInfoForm.userId = obj.id;
      this.auctionInfoForm.headimgUrl = obj.headimgUrl;
      this.auctionInfoForm.nickName = obj.nickName;
      this.userBoxVisible = false;
    },
    handlePhoneBlur() {
      // 手机号输入框失去焦点时，自动将时间条件设置为"不限"
      if (this.searchForm.phone) {
        this.searchForm.dateType = '2';
        this.paramsSearch.phone = this.searchForm.phone;
      } else {
        delete this.paramsSearch.phone;
      }
    },
    handlePhoneClear() {
      // 手机号输入框清除时，重置时间条件
      this.searchForm.dateType = '0';
      delete this.paramsSearch.phone;
    },
    // 清空手机号参数
    clearPhoneParam() {
      this.handlePhoneClear();
      this.searchForm.phone = '';
    },
    // 根据订单状态返回对应的标签类型
    getOrderStatusTagType(statusDesc) {
      // 添加空值检查
      if (!statusDesc) {
        return '' // 默认样式
      }
      
      if (statusDesc === '已完成') {
        return 'success'
      } else if (statusDesc === '已取消') {
        return 'danger' // Element UI的el-tag使用danger表示错误状态
      } else {
        return '' // 默认样式
      }
    },
    // 获取最新的退款状态
    getLatestRefundStatus(row) {
      // 假设档期数据中有订单信息，包含退款状态
      if (row.orderInfo && row.orderInfo.listOrderRefunds && row.orderInfo.listOrderRefunds.length > 0) {
        const latestRefund = row.orderInfo.listOrderRefunds[row.orderInfo.listOrderRefunds.length - 1]
        // 添加空值检查
        if (latestRefund) {
          return latestRefund.statusDesc || latestRefund.status || '-'
        }
      }
      return null
    },
    // 根据退款状态返回对应的标签类型
    getRefundStatusTagType(statusDesc) {
      // 添加空值检查
      if (!statusDesc) {
        return 'info' // 默认使用info灰色
      }
      
      if (statusDesc === '退款申请中') {
        return 'warning' // 黄色
      } else if (statusDesc === '同意退款') {
        return 'danger' // 红色
      } else if (statusDesc === '拒绝') {
        return '' // primary蓝色（默认）
      } else {
        return 'info' // 其他状态用info灰色
      }
    },
    // 获取购买详情
    getPurchaseDetail(row) {
      // 假设档期数据中有订单信息，包含商品详情
      if (row.orderInfo && row.orderInfo.listOrderItem && row.orderInfo.listOrderItem.length > 0) {
        const firstItem = row.orderInfo.listOrderItem[0]
        // 添加空值检查
        if (firstItem) {
          return `${firstItem.spuName}|${firstItem.specInfo}`
        }
      }
      return null
    }
  }
}
</script>
<style lang="scss" scoped>
.shop_room_tree /deep/ {
  .el-tree-node__content {
    padding: 5px;
    margin: 2px;

    .el-tree-node__expand-icon {
      justify-content: start;
    }
  }
}

.tree_content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.del_detail {
  background-color: #d0a9a9;
}
</style>
